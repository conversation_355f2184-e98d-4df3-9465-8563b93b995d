package com.mchz.dcas.server.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.mchz.dcas.client.model.request.SourceMetaRequest;
import com.mchz.dcas.server.entity.IndicatorDatasource;
import com.mchz.dcas.server.entity.StandardItem;
import com.mchz.dcas.server.enums.IndicatorTypeEnum;
import com.mchz.dcas.server.mapper.IndicatorDatasourceMapper;
import com.mchz.dcas.server.mapper.StandardItemMapper;
import com.mchz.dcas.server.mapper.ThreatFrequencyMapper;
import com.mchz.dcas.server.mapper.ThreatTreeMapper;
import com.mchz.dcas.server.model.dto.IndicatorDatasourceDTO;
import com.mchz.dcas.server.model.dto.MidVulDTO;
import com.mchz.dcas.server.model.param.IndicatorSourceSearchParam;
import com.mchz.dcas.server.model.vo.IndicatorDatasourceVO;
import com.mchz.dcas.server.model.vo.KeyValue;
import com.mchz.dcas.server.model.vo.KeyValueTypeVO;
import com.mchz.dcas.server.model.vo.TableColumnVO;
import com.mchz.dcas.server.service.IIndicatorDatasourceService;
import com.mchz.dcas.server.utils.DcasUtils;
import com.mchz.dcas.server.utils.PageResult;
import com.mchz.mcdatasource.core.callback.MetaCallBackInterface;
import com.mchz.mcdatasource.core.engine.dbobject.DbObjectEngine;
import com.mchz.mcdatasource.model.MetaDataStreamRequest;
import com.mchz.mcdatasource.model.MetaSchema;
import com.mchz.mcdatasource.model.core.StreamDataType;
import com.mchz.mcdatasource.model.db.DatasourceDatabase;
import com.mchz.mcdatasource.model.db.DatasourceDatabaseMeta;
import com.mchz.mcdatasource.model.db.exception.DatabaseException;
import com.mchz.mcdatasource.utils.CloseResourcesUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.net.URI;
import java.sql.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 指标数源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class IndicatorDatasourceServiceImpl extends ServiceImpl<IndicatorDatasourceMapper, IndicatorDatasource>
    implements IIndicatorDatasourceService {

    private final StandardItemMapper standardItemMapper;
    private final IndicatorDatasourceMapper indicatorDatasourceMapper;
    private final ThreatFrequencyMapper threatFrequencyMapper;
    private final ThreatTreeMapper threatTreeMapper;

    @Value("${dcas.sql-dump.tables}")
    private String cocTables;
    @Value("${spring.datasource.url}")
    private String url;
    @Value("${spring.datasource.username}")
    private String username;
    @Value("${spring.datasource.password}")
    private String password;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void add(IndicatorDatasourceDTO dto) {
        IndicatorDatasource indicatorDatasource =
            IndicatorDatasource.builder().tableName(dto.getTableName()).tableComment(dto.getTableComment())
                .columnName(dto.getColumnName()).columnComment(dto.getColumnComment()).createTime(LocalDateTime.now())
                .updateTime(LocalDateTime.now()).createBy(DcasUtils.getUserAccount())
                .updateBy(DcasUtils.getUserAccount()).status(dto.getStatus()).build();
        indicatorDatasourceMapper.insert(indicatorDatasource);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void edit(IndicatorDatasourceDTO dto) {
        IndicatorDatasource indicatorDatasource =
            IndicatorDatasource.builder().id(dto.getId()).tableName(dto.getTableName())
                .tableComment(dto.getTableComment()).columnName(dto.getColumnName())
                .columnComment(dto.getColumnComment()).updateTime(LocalDateTime.now())
                .updateBy(DcasUtils.getUserAccount()).status(dto.getStatus()).build();
        indicatorDatasourceMapper.updateById(indicatorDatasource);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        indicatorDatasourceMapper.deleteById(id);
    }

    @Override
    public PageResult<IndicatorDatasourceVO> pageList(IndicatorSourceSearchParam indicatorSourceSearchParam) {
        try (Page<Object> page = PageHelper
            .startPage(indicatorSourceSearchParam.getCurrentPage(), indicatorSourceSearchParam.getPageSize())) {
            QueryWrapper<IndicatorDatasource> queryWrapper = new QueryWrapper<>();
            queryWrapper
                .eq(indicatorSourceSearchParam.getStatus() != null, "status", indicatorSourceSearchParam.getStatus());
            queryWrapper.like(StringUtils.isNotEmpty(indicatorSourceSearchParam.getTableName()), "table_name",
                indicatorSourceSearchParam.getTableName());
            queryWrapper.like(StringUtils.isNotEmpty(indicatorSourceSearchParam.getColumnName()), "column_name",
                indicatorSourceSearchParam.getColumnName());
            queryWrapper.like(StringUtils.isNotEmpty(indicatorSourceSearchParam.getColumnComment()), "column_comment",
                indicatorSourceSearchParam.getColumnComment());
            queryWrapper.orderByDesc("update_time");
            List<IndicatorDatasource> list = indicatorDatasourceMapper.selectList(queryWrapper);
            List<IndicatorDatasourceVO> voList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(indicatorDatasource -> {
                    IndicatorDatasourceVO vo = new IndicatorDatasourceVO();
                    BeanUtils.copyProperties(indicatorDatasource, vo);
                    voList.add(vo);
                });
            }
            return PageResult.ofPage(page.getTotal(), voList);
        }
    }

    @Override
    public void updateIndicatorType(IndicatorTypeEnum indicatorType, Long id) {
        IndicatorDatasource indicatorDatasource = new IndicatorDatasource();
        if (indicatorType != null) {
            indicatorDatasource.setType(indicatorType.getType());
        } else {
            indicatorDatasource.setType("");
        }
        indicatorDatasourceMapper.update(indicatorDatasource, new UpdateWrapper<IndicatorDatasource>().eq("id", id));
    }

    @Override
    public IndicatorDatasourceVO detail(Long id) {
        IndicatorDatasource indicatorDatasource = indicatorDatasourceMapper.selectById(id);
        IndicatorDatasourceVO vo = new IndicatorDatasourceVO();
        BeanUtils.copyProperties(indicatorDatasource, vo);
        return vo;
    }

    @Override
    public List<KeyValueTypeVO> getIndicatorsByType(IndicatorTypeEnum type) {
        QueryWrapper<IndicatorDatasource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 0);
        if (type != null) {
            queryWrapper.eq("type", type.getType());
        }
        List<IndicatorDatasource> indicatorDatasourceList = indicatorDatasourceMapper.selectList(queryWrapper);
        List<KeyValueTypeVO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(indicatorDatasourceList)) {
            return list;
        }

        List<String> cocTableList = new ArrayList<>();
        if (cocTables != null) {
            String[] tableArr = cocTables.split(",");
            cocTableList = Arrays.asList(tableArr);
        }
        List<String> finalCocTableList = cocTableList;
        indicatorDatasourceList.forEach(indicatorDatasource -> {
            KeyValueTypeVO keyValueVO = new KeyValueTypeVO();
            keyValueVO.setKey(indicatorDatasource.getId());
            keyValueVO.setValue(indicatorDatasource.getTableName() + "." + indicatorDatasource.getColumnName());
            if (finalCocTableList.contains(indicatorDatasource.getTableName())) {
                // 后端表格
                keyValueVO.setType(1);
            } else {
                // 前端表格
                keyValueVO.setType(2);
            }
            list.add(keyValueVO);
        });
        return list;
    }

    @Override
    public Boolean flush() {
        log.info("刷新数据源开始...");
        if (url == null) {
            log.warn("jdbc url is null");
            return false;
        }
        String jdbcUrl = url.substring(5);
        URI uri = URI.create(jdbcUrl);
        String db = "dcas";
//        if (uri.getPath() != null) {
//            db = uri.getPath().substring(1);
//        }

        DatasourceDatabaseMeta datasourceDatabaseMeta =
            new DatasourceDatabaseMeta("pgsql", uri.getHost(), db, String.valueOf(uri.getPort()), username, password);
        MetaDataStreamRequest request = new MetaDataStreamRequest();
        List<StreamDataType> dataTypes = new ArrayList<>();
        dataTypes.add(StreamDataType.TABLE_COMMENTS);
        dataTypes.add(StreamDataType.TABLE_COLUMN);
        dataTypes.add(StreamDataType.COLUMN_COMMENT);
        request.setDataTypes(dataTypes);
        request.setSchema("public");
        request.setSourceId(0);
        String appVersion = getAppVersion(datasourceDatabaseMeta);
        DbObjectEngine.getInstance()
            .getMetaDataByStream(datasourceDatabaseMeta.getDatabaseMeta(), request, new MyCallBack(appVersion));

        return true;
    }

    @Override
    public Boolean confirm(Long id) {
        IndicatorDatasource indicatorDatasource = new IndicatorDatasource();
        indicatorDatasource.setStatus(0);
        indicatorDatasourceMapper
            .update(indicatorDatasource, new UpdateWrapper<IndicatorDatasource>().eq("id", id).eq("status", 2));
        return true;
    }

    @Override
    public Set<TableColumnVO> getMidTable(String tableName, Integer fileId) {
        QueryWrapper<IndicatorDatasource> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("type", "BUILDIN");
        if (StrUtil.isNotEmpty(tableName))
            queryWrapper.eq("table_name", tableName);
        List<IndicatorDatasource> tables = indicatorDatasourceMapper.selectList(queryWrapper);
        if (StrUtil.isEmpty(tableName))
            return tables.stream().map(t ->
                    TableColumnVO.builder().tableName(t.getTableName()).tableComment(t.getTableComment()).build()).collect(Collectors.toSet());
        return tables.stream().map(t -> {
                    TableColumnVO build = TableColumnVO.builder()
                            .tableName(t.getTableName())
                            .tableComment(t.getTableComment())
                            .columnName(t.getColumnName())
                            .columnType(t.getColumnType())
                            .columnComment(t.getColumnComment())
                            .build();
                    build.setOptions(getBuiltInData(build, fileId));
                    return build;
                }
        ).collect(Collectors.toSet());
    }

    /**
     * 根据表字段获取字段可选下拉内容
     *
     */
    private List<KeyValue> getBuiltInData(TableColumnVO vo, Integer fileId) {
        List<KeyValue> res = new ArrayList<>();
        String tableName = vo.getTableName();
        String columnName = vo.getColumnName();
        if (Objects.equals("mid_operation", tableName)) {
            res = operationBuildInData(columnName);
        } else if (Objects.equals("mid_inventory_result", tableName)) {
            res = inventoryBuildInData(columnName);
        } else if (Objects.equals("mid_vul_result", tableName)) {
            res = vulBuildInData(columnName, fileId);
        } else if (Objects.equals("mid_threat_result", tableName)) {
            res = threatBuildInData(columnName);
        }
        return res;
    }

    private List<KeyValue> vulBuildInData(String columnName, Integer fileId) {
        List<KeyValue> res = new ArrayList<>();
        List<MidVulDTO> midVulList = standardItemMapper.selectMidVul(fileId);
        if (Objects.equals("standard_id", columnName)) {
            res.addAll(midVulList.stream().map(m -> new KeyValue(m.getFileName(), m.getFileId())).collect(Collectors.toSet()));
        } else if (Objects.equals("item_id", columnName)) {
            res.addAll(midVulList.stream().map(m -> new KeyValue(m.getItemName(), m.getItemId())).collect(Collectors.toSet()));
        } else if (Objects.equals("dimension", columnName)) {
            res.addAll(midVulList.stream().map(m -> new KeyValue(m.getDimension(), m.getDimension())).collect(Collectors.toSet()));
        } else if (Objects.equals("bp_code", columnName)) {
            res.addAll(midVulList.stream().map(m -> new KeyValue(m.getBpCode(), m.getBpCode())).collect(Collectors.toSet()));
        } else if (Objects.equals("process_tag", columnName)) {
            res.addAll(midVulList.stream().map(m -> new KeyValue(m.getStage(), m.getStage())).collect(Collectors.toSet()));
        }
        return res;
    }

    private List<KeyValue> threatBuildInData(String columnName) {
        List<KeyValue> result = new ArrayList<>();
        if (Objects.equals("threat_type", columnName)) {
            result.addAll(threatTreeMapper.selectTreeTypeKeyValues(true));
        } else if (Objects.equals("threat_child", columnName)) {
            result.addAll(threatTreeMapper.selectTreeTypeKeyValues(false));
        } else if (Objects.equals("threat_frequency_tag", columnName)) {
            result.addAll(threatFrequencyMapper.selectFrequencyTagKeyValues());
        } else if (Objects.equals("threat_frequency_level", columnName)) {
            result.addAll(threatFrequencyMapper.selectFrequencyLevelKeyValues());
        }
        return result;
    }

    private List<KeyValue> inventoryBuildInData(String columnName) {
        List<KeyValue> result = new ArrayList<>();
        if (Objects.equals("risk_source_dimension", columnName)) {
            result.addAll(standardItemMapper.selectColumnData());
        } else if (Objects.equals("safety_impact", columnName)) {
            result.add(new KeyValue("限制个人自主决定权", "限制个人自主决定权"));
            result.add(new KeyValue("引发差别性待遇", "引发差别性待遇"));
            result.add(new KeyValue("个人名誉受损和遭受精神压力", "个人名誉受损和遭受精神压力"));
            result.add(new KeyValue("人身财产受损", "人身财产受损"));
        } else if (Objects.equals("sensitive_level", columnName)) {
            result.add(new KeyValue("1级", "1"));
            result.add(new KeyValue("2级", "2"));
            result.add(new KeyValue("3级", "3"));
            result.add(new KeyValue("4级", "4"));
            result.add(new KeyValue("5级", "5"));
        } else if (Objects.equals("data_tag", columnName)) {
            result.add(new KeyValue("一般数据", "GENERAL"));
            result.add(new KeyValue("重要数据", "IMPORTANT"));
            result.add(new KeyValue("核心数据", "CORE"));
        }
        return result;
    }

    private List<KeyValue> operationBuildInData(String columnName) {
        List<KeyValue> result = new ArrayList<>();
        if (Objects.equals("highest_sensitive_level", columnName)) {
            result.add(new KeyValue("1级", "1"));
            result.add(new KeyValue("2级", "2"));
            result.add(new KeyValue("3级", "3"));
            result.add(new KeyValue("4级", "4"));
            result.add(new KeyValue("5级", "5"));
        } else if (Objects.equals("bus_system_tag", columnName)) {
            result.add(new KeyValue("一般系统", "GENERAL_SYSTEM"));
            result.add(new KeyValue("重要系统", "IMPORTANT_SYSTEM"));
            result.add(new KeyValue("核心系统", "CORE_SYSTEM"));
        }
        return result;
    }


    private String getAppVersion(DatasourceDatabaseMeta datasourceDatabaseMeta){
        DatasourceDatabase datasourceDatabase = new DatasourceDatabase(datasourceDatabaseMeta);
        ResultSet rs = null;
        String appVersion = "";
        try {
            datasourceDatabase.connect();
            rs = datasourceDatabase.openQuery("select config_value from sys_config where config_key = 'sys.app.version'");
            if (rs.next()){
                appVersion = rs.getString("config_value") ;
            }

        } catch (DatabaseException | SQLException e) {
            log.error("execute sql failed!", e);
        } finally {
            CloseResourcesUtils.close(rs);
            try {
                datasourceDatabase.close();
            } catch (IOException e) {
                log.error("close datasourceDatabase failed!", e);
            }
        }
        return appVersion;
    }

    private class MyCallBack implements MetaCallBackInterface<MetaSchema> {
        /**
         * 表格数
         */
        private int tableCount = 0;

        /**
         * se端版本号
         */
        private final String version;

        /**
         * 字段列表
         */
        private final List<SourceMetaRequest.SourceColumnMeta> columnList = new ArrayList<>();

        public MyCallBack(String appVersion) {
            this.version = appVersion;
        }

        @Override
        public void process(MetaSchema metaSchema) {
            tableCount += metaSchema.getTables().size();
            metaSchema.getTables().forEach(metaTable -> {
                // 过滤掉cron相关表格
                if (metaTable.getName().startsWith("qrtz_") || metaTable.getName().startsWith("sys_")  || metaTable.getName().startsWith("mid_")) {
                    return;
                }

                metaTable.getColumns().forEach(metaColumn -> {
                    SourceMetaRequest.SourceColumnMeta sourceColumnMeta = new SourceMetaRequest.SourceColumnMeta();
                    sourceColumnMeta.setTableName(metaTable.getName());
                    sourceColumnMeta.setTableComment(metaTable.getRemarks());
                    sourceColumnMeta.setColumnName(metaColumn.getName());
                    sourceColumnMeta.setColumnComment(metaColumn.getRemarks());
                    sourceColumnMeta.setColumnLength(metaColumn.getColumnLength());
                    sourceColumnMeta.setColumnType(metaColumn.getType());
                    sourceColumnMeta.setVersion(version);
                    columnList.add(sourceColumnMeta);
                });
            });
        }

        @Override
        public void processError(Exception e) {
            log.error("process error !", e);
        }

        @Override
        public void processFinish() {
            int columnCount = columnList.size();

            SourceMetaRequest sourceMetaRequest = new SourceMetaRequest();
            sourceMetaRequest.setColumnMetaList(columnList);
            sourceFlush(sourceMetaRequest);
            log.info("刷新数据源结束...表格数{}，字段数{}", tableCount, columnCount);
        }
    }

    private Boolean sourceFlush(SourceMetaRequest request) {
        List<IndicatorDatasource> newColumnList = new ArrayList<>();
        List<IndicatorDatasource> deleteColumnList = new ArrayList<>();
        List<IndicatorDatasource> oldList = this.list();
        Map<String, IndicatorDatasource> map = new HashMap<>(16);
        // 是否第一次刷新
        if (CollectionUtils.isEmpty(oldList)){
            // 情况一: 数据库不存在，第一次数源刷新
            newColumnList.addAll(buildIndicatorDatasourceList(request.getColumnMetaList(),map, 0));
        } else {
            map = oldList.stream().collect(Collectors.toMap(IndicatorDatasource::key, Function.identity()));
            // 情况二：数据库不存在，新增字段
            newColumnList.addAll(buildIndicatorDatasourceList(request.getColumnMetaList(),map, 2));
        }

        // 情况三：数据库存在，删除字段
        Map<String, SourceMetaRequest.SourceColumnMeta> newMap = request.getColumnMetaList().stream().collect(Collectors.toMap(
            SourceMetaRequest.SourceColumnMeta::key,Function.identity()));
        for (IndicatorDatasource indicatorDatasource : oldList){
            String key = indicatorDatasource.getTableName() + "_" + indicatorDatasource.getColumnName();
            if (!newMap.containsKey(key)){
                indicatorDatasource.setStatus(1);
                indicatorDatasource.setUpdateTime(LocalDateTime.now());
                indicatorDatasource.setUpdateBy(DcasUtils.getUserAccount());
                deleteColumnList.add(indicatorDatasource);
            }
        }
        this.saveBatch(newColumnList);
        this.updateBatchById(deleteColumnList);
        return true;
    }

    private List<IndicatorDatasource> buildIndicatorDatasourceList(List<SourceMetaRequest.SourceColumnMeta> sourceColumnMetaList,
        Map<String, IndicatorDatasource> map, int status) {
        List<IndicatorDatasource> list = new ArrayList<>();
        for (SourceMetaRequest.SourceColumnMeta sourceColumnMeta : sourceColumnMetaList) {
            String key = sourceColumnMeta.getTableName() + "_" + sourceColumnMeta.getColumnName();
            if (map.containsKey(key)) {
                continue;
            }
            IndicatorDatasource indicatorDatasource = new IndicatorDatasource();
            BeanUtils.copyProperties(sourceColumnMeta, indicatorDatasource);
            indicatorDatasource.setCreateTime(LocalDateTime.now());
            indicatorDatasource.setUpdateTime(LocalDateTime.now());
            indicatorDatasource.setCreateBy(DcasUtils.getUserAccount());
            indicatorDatasource.setUpdateBy(DcasUtils.getUserAccount());
            indicatorDatasource.setStatus(status);
            indicatorDatasource.setColumnLength(sourceColumnMeta.getColumnLength());
            indicatorDatasource.setColumnType(sourceColumnMeta.getColumnType());
            indicatorDatasource.setVersion(sourceColumnMeta.getVersion());
            list.add(indicatorDatasource);
        }
        return list;
    }

    public static void main(String[] args) {
        String url = "jdbc:postgresql://**************:65432/dcas";
        String username = "dps";
        String password = "hzmcdps";
//        String sql = "select * from public.co_operation";
        String sql =
            "select\n" + "\tcol.table_schema,\n" + "\tcol.table_name,\n" + "\tcol.column_name,\n" + "\tcol.data_type,\n" + "\tcol.character_maximum_length,\n" + "\tcol.is_nullable,\n" + "\tcol.numeric_precision,\n" + "\tcol.numeric_scale,\n" + "\tdsc.description,\n" + "\tcol.ordinal_position,\n" + "\tcol.udt_name as col_arry_type,\n" + "\tpa.attndims,\n" + "\tcol.column_default\n" + "from\n" + "\tinformation_schema.columns col\n" + "left join pg_catalog.pg_namespace pns on\n" + "\tcol.table_schema = pns.nspname\n" + "left join pg_catalog.pg_class pc on\n" + "\tcol.table_name = pc.relname\n" + "\tand pc.relnamespace = pns.oid\n" + "left join pg_attribute pa on\n" + "\t(col.column_name = pa.attname)\n" + "\tand pa.attrelid = pc.oid\n" + "left join pg_catalog.pg_description dsc on\n" + "\tpc.oid = dsc.objoid\n" + "\tand col.ordinal_position = dsc.objsubid\n" + "where\n" + "\tcol.table_schema not in ( 'pg_catalog', 'information_schema', 'pg_toast', 'pg_temp_1', 'pg_toast_temp_1', 'gp_toolkit', 'pg_aoseg', 'pg_bitmapindex', 'sys' )\n" + "\tand pc.relkind in ('r', 't', 'f', 'p') and table_schema ='public' and table_name like '%mid_%'";
//        Database database = new Database();
//        database.execStatement(sql);
        Connection connection = null;
        System.out.println(sql);
        try {
            Class.forName("org.postgresql.Driver");
            connection = DriverManager.getConnection(url, username, password);
            PreparedStatement ps = connection.prepareStatement(sql);
            ResultSet rs = ps.executeQuery();
            while (rs.next()) {
                String schema = rs.getString("table_schema");
                String table = rs.getString("table_name");
                String table_comment = "";
                switch (table){
                    case "mid_operation":
                        table_comment = "作业信息表";
                        break;
                    case "mid_inventory_result":
                        table_comment = "资产数据结果采集表";
                        break;
                    case "mid_vul_result":
                        table_comment = "脆弱性数据结果采集表";
                        break;
                    case "mid_threat_result":
                        table_comment = "威胁数据结果采集表";
                        break;
                    default:

                }
                String column = rs.getString("column_name");
                String column_comment = rs.getString("description");
                String character_maximum_length = rs.getString("character_maximum_length");
                String numeric_precision = rs.getString("numeric_precision");
                String numeric_scale = rs.getString("numeric_scale");

                String column_length = StrUtil.isEmpty(character_maximum_length) ? ( StrUtil.isEmpty(numeric_precision) ?  "0" : numeric_precision) : character_maximum_length;
                String col_arry_type = rs.getString("col_arry_type");

                String insertSql = String.format("INSERT INTO public.indicator_datasource\n" + "                    " +
                    "(table_name, table_comment, column_name, column_comment, status, \"type\", create_time, update_time, create_by, update_by, \"version\", column_length, column_type)\n"
                     +
                    "VALUES('%s', '%s', '%s', '%s', 0, 'BUILDIN', now(), now(), 'manager', 'manager', '2.1.0', '%s', '%s');", table, table_comment,column,column_comment,column_length,col_arry_type);
                System.out.println(insertSql);
            }
            rs.close();
            ps.close();
        } catch (SQLException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            throw new RuntimeException(e);
        } finally {
            CloseResourcesUtils.close(connection);
        }
    }
}
