package com.mchz.dcas.server.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mchz.dcas.server.entity.ApiInterface;
import com.mchz.dcas.server.mapper.ApiInterfaceMapper;
import com.mchz.dcas.server.model.param.ApiInterfaceParam;
import com.mchz.dcas.server.model.param.InParam;
import com.mchz.dcas.server.model.param.OutParam;
import com.mchz.dcas.server.service.IApiInterfaceService;
import com.mchz.dcas.server.utils.DcasUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 对接产品api接口 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Service
public class ApiInterfaceServiceImpl extends ServiceImpl<ApiInterfaceMapper, ApiInterface>
    implements IApiInterfaceService {

    @Override
    public void detail(Long id) {

    }

    @Override
    public void saveApiInterface(List<ApiInterfaceParam> apiInterfaceList, Long releaseId) {
        List<ApiInterface> list = new ArrayList<>();
        apiInterfaceList.forEach(apiInterfaceParam -> {
            ApiInterface apiInterface = new ApiInterface();
            BeanUtils.copyProperties(apiInterfaceParam, apiInterface);
            apiInterface.setReqParams(JSONUtil.toJsonStr(apiInterfaceParam.getInParam()));
            apiInterface.setReqResult(JSONUtil.toJsonStr(apiInterfaceParam.getOutParam()));
            apiInterface.setCreateTime(LocalDateTime.now());
            apiInterface.setUpdateTime(LocalDateTime.now());
            apiInterface.setCreateUser(DcasUtils.getUserAccount());
            apiInterface.setUpdateUser(DcasUtils.getUserAccount());
            apiInterface.setReleaseId(releaseId);
            list.add(apiInterface);
        });
        this.saveBatch(list);
    }

    @Override
    public void deleteApiInterfaceByReleaseId(Long releaseId) {
        this.remove(new QueryWrapper<ApiInterface>().eq("release_id", releaseId));
    }

    @Override
    public List<ApiInterfaceParam> getApiInterfaceList(Long releaseId) {
        List<ApiInterface> list = this.list(new QueryWrapper<ApiInterface>().eq("release_id", releaseId).orderByAsc("sort"));
        List<ApiInterfaceParam> apiInterfaceParams = new ArrayList<>();
        list.forEach(apiInterface -> {
            ApiInterfaceParam param = new ApiInterfaceParam();
            BeanUtils.copyProperties(apiInterface, param);
            param.setInParam(JSONUtil.toBean(apiInterface.getReqParams(), InParam.class));
            param.setOutParam(JSONUtil.toBean(apiInterface.getReqResult(), OutParam.class));
            apiInterfaceParams.add(param);
        });
        return apiInterfaceParams;
    }
}
