package com.mchz.dcas.server.model.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.mchz.dcas.server.model.param.ApiInterfaceParam;
import com.mchz.dcas.server.model.param.AuthParameter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @className ApiReleaseVO
 * @description api版本信息VO
 * @date 2025/06/09 18:00
 */
@Getter
@Setter
@Builder
public class ApiReleaseVO {

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;

    /**
     * 产品类型名称
     */
    @ApiModelProperty("产品类型名称")
    private String name;

    /**
     * 对接产品能力
     */
    @ApiModelProperty("对接产品能力")
    private String capability;

    /**
     * 所属公司
     */
    @ApiModelProperty("所属公司")
    private String company;

    /**
     * 产品logo
     */
    @ApiModelProperty("产品logo")
    private String logo;

    /**
     * 产品授权字段参数
     */
    @ApiModelProperty("产品授权字段参数")
    private String auth;

    /**
     * 产品授权参数配置规则
     */
    @ApiModelProperty("产品授权参数配置规则")
    private List<AuthParameter> authConfig;

    /**
     * 能力标签类型
     */
    @ApiModelProperty("能力标签类型")
    private String ability;

    /**
     * 应用标签
     */
    @ApiModelProperty("应用标签")
    private String appTag;

    /**
     * 是否启用
     */
    @ApiModelProperty("是否启用")
    private Boolean enable;

    /**
     * 版本号，从1开始，根据页面触发一次递增1
     */
    @ApiModelProperty("版本号，从1开始，根据页面触发一次递增1")
    private Integer version;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createUser;

    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updateUser;


    @ApiModelProperty(value = "api接口列表")
    private List<ApiInterfaceParam> apiInterfaceList;
}
