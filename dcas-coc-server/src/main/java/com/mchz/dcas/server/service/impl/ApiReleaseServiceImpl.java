package com.mchz.dcas.server.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.mchz.dcas.server.common.exception.ServiceException;
import com.mchz.dcas.server.entity.ApiInterface;
import com.mchz.dcas.server.entity.ApiRelease;
import com.mchz.dcas.server.mapper.ApiReleaseMapper;
import com.mchz.dcas.server.model.param.ApiInterfaceParam;
import com.mchz.dcas.server.model.param.ApiReleaseParam;
import com.mchz.dcas.server.model.param.AuthParameter;
import com.mchz.dcas.server.model.req.ApiReleaseSearchReq;
import com.mchz.dcas.server.model.req.IdsReq;
import com.mchz.dcas.server.model.vo.ApiReleaseVO;
import com.mchz.dcas.server.service.IApiInterfaceService;
import com.mchz.dcas.server.service.IApiReleaseService;
import com.mchz.dcas.server.utils.DcasUtils;
import com.mchz.dcas.server.utils.Func;
import com.mchz.dcas.server.utils.PageResult;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * api版本记录，包含产品类型、产品所属公司、产品logo以及产品对接参数（token）等 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@RequiredArgsConstructor
@Service
public class ApiReleaseServiceImpl extends ServiceImpl<ApiReleaseMapper, ApiRelease> implements IApiReleaseService {

    private final IApiInterfaceService iApiInterfaceService;
    private final ObjectMapper objectMapper;

    @Override
    public void deleteApiRelease(IdsReq req) {
        req.getIds().forEach(id -> this.baseMapper.deleteById(id));
    }

    @Override
    public PageResult<ApiReleaseVO> list(ApiReleaseSearchReq req) {
        try (Page<Object> page = PageHelper.startPage(req.getCurrentPage(), req.getPageSize())) {
            QueryWrapper<ApiRelease> queryWrapper = new QueryWrapper<>();
            if (StrUtil.isNotEmpty(req.getName())){
                queryWrapper.like("name", req.getName());
            }
            if (StrUtil.isNotEmpty(req.getCompany())){
                queryWrapper.like("company", req.getCompany());
            }
            if (StrUtil.isNotEmpty(req.getCapability())){
                queryWrapper.eq("capability", req.getCapability());
            }
            if (StrUtil.isNotEmpty(req.getAppTag())){
                queryWrapper.like("app_tag", req.getAppTag());
            }
            if (StrUtil.isNotEmpty(req.getAbility())){
                queryWrapper.like("ability", req.getAbility());
            }
            queryWrapper.orderByDesc("update_time");
            List<ApiRelease> list = baseMapper.selectList(queryWrapper);

            List<ApiReleaseVO> apiReleaseVOList = new ArrayList<>();
            list.forEach(apiRelease -> {
                apiReleaseVOList.add(convert(apiRelease));
            });
            return PageResult.ofPage(page.getTotal(), apiReleaseVOList);
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    @SneakyThrows
    public Long add(ApiReleaseParam param) {
        // 再保存api版本信息
        ApiRelease apiRelease = new ApiRelease();
        BeanUtils.copyProperties(param, apiRelease);
        apiRelease.setCreateTime(LocalDateTime.now());
        apiRelease.setUpdateTime(LocalDateTime.now());
        apiRelease.setCreateUser(DcasUtils.getUserAccount());
        apiRelease.setUpdateUser(DcasUtils.getUserAccount());
        apiRelease.setAuthConfig(serializeAuthConfig(param.getAuthConfig()));
        apiRelease.setVersion(1);
        apiRelease.setEnable(false);
        baseMapper.insert(apiRelease);

        // 先保存api接口信息
        if (CollUtil.isNotEmpty(param.getApiInterfaceList())) {
            iApiInterfaceService.saveApiInterface(param.getApiInterfaceList(), apiRelease.getId());
        }

        return apiRelease.getId();
    }

    @Override
    @SneakyThrows
    public void updateApiRelease(ApiReleaseParam param) {
        // 先删除api接口信息，后保存
        if (CollUtil.isNotEmpty(param.getApiInterfaceList())) {
            iApiInterfaceService.deleteApiInterfaceByReleaseId(param.getId());
            iApiInterfaceService.saveApiInterface(param.getApiInterfaceList(), param.getId());
        }

        // 再更新api版本信息
        ApiRelease apiRelease = new ApiRelease();
        BeanUtils.copyProperties(param, apiRelease);
        apiRelease.setUpdateTime(LocalDateTime.now());
        apiRelease.setUpdateUser(DcasUtils.getUserAccount());
        apiRelease.setAuthConfig(objectMapper.writeValueAsString(param.getAuthConfig()));
        baseMapper.updateById(apiRelease);
    }

    @Override
    public void enbale(Long id) {
        List<ApiInterfaceParam> apiInterfaceList = iApiInterfaceService.getApiInterfaceList(id);
        if (CollUtil.isEmpty(apiInterfaceList)){
            throw new ServiceException("接口列表为空,无法启用!");
        }
        ApiRelease apiRelease = baseMapper.selectById(id);
        apiRelease.setEnable(!apiRelease.getEnable());
        baseMapper.updateById(apiRelease);
    }

    @Override
    public void updateVersion(Long id) {
        ApiRelease apiRelease = baseMapper.selectById(id);
        int newVersion = apiRelease.getVersion() + 1;
        ApiRelease update = new ApiRelease();
        update.setId(id);
        update.setVersion(newVersion);
        baseMapper.updateById(update);
    }

    @Override
    public ApiReleaseVO detail(Long id) {
        ApiRelease apiRelease = baseMapper.selectById(id);
        return convert(apiRelease);
    }

    private ApiReleaseVO convert(ApiRelease apiRelease) {
        List<AuthParameter> authConfigList = parseAuthConfig(apiRelease.getAuthConfig());
        return ApiReleaseVO.builder().id(apiRelease.getId()).ability(apiRelease.getAbility()).auth(apiRelease.getAuth())
            .authConfig(authConfigList)
            .logo(apiRelease.getLogo()).appTag(apiRelease.getAppTag()).name(apiRelease.getName())
            .capability(apiRelease.getCapability()).company(apiRelease.getCompany())
            .createUser(apiRelease.getCreateUser()).updateTime(apiRelease.getUpdateTime())
            .version(apiRelease.getVersion()).updateUser(apiRelease.getUpdateUser())
            .createTime(apiRelease.getCreateTime()).enable(apiRelease.getEnable())
            .apiInterfaceList(iApiInterfaceService.getApiInterfaceList(apiRelease.getId())).build();
    }

    /**
     * 解析 authConfig 字符串为 AuthParameter 列表，支持多态子类型
     * @param authConfigJson JSON 字符串
     * @return AuthParameter 列表
     */
    private List<AuthParameter> parseAuthConfig(String authConfigJson) {
        if (StrUtil.isBlank(authConfigJson)) {
            return Collections.emptyList();
        }

        try {
            // 使用 Jackson ObjectMapper 进行多态反序列化
            // Jackson 会根据 @JsonTypeInfo 和 @JsonSubTypes 注解自动识别子类型
            return objectMapper.readValue(authConfigJson, new TypeReference<List<AuthParameter>>() {});
        } catch (Exception e) {
            // 如果解析失败，记录日志并返回空列表，避免影响整体功能
            throw new ServiceException("解析 authConfig 失败");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean replica(Long id) {
        ApiReleaseVO old = detail(id);
        ApiRelease apiRelease = new ApiRelease();
        BeanUtils.copyProperties(old, apiRelease);
        apiRelease.setId(null);
        apiRelease.setName(rename(apiRelease.getName()));
        apiRelease.setEnable(false);
        apiRelease.setCreateUser(DcasUtils.getUserAccount());
        apiRelease.setUpdateUser(DcasUtils.getUserAccount());
        apiRelease.setCreateTime(LocalDateTime.now());
        apiRelease.setUpdateTime(LocalDateTime.now());
        save(apiRelease);

        List<ApiInterface> apiInterfaceList = new ArrayList<>();
        old.getApiInterfaceList().forEach(apiInterfaceParam -> {
            ApiInterface apiInterface = new ApiInterface();
            BeanUtils.copyProperties(apiInterfaceParam, apiInterface);
            apiInterface.setReqParams(JSONUtil.toJsonStr(apiInterfaceParam.getInParam()));
            apiInterface.setReqResult(JSONUtil.toJsonStr(apiInterfaceParam.getOutParam()));
            apiInterface.setCreateUser(DcasUtils.getUserAccount());
            apiInterface.setUpdateUser(DcasUtils.getUserAccount());
            apiInterface.setCreateTime(LocalDateTime.now());
            apiInterface.setUpdateTime(LocalDateTime.now());
            apiInterface.setId(null);
            apiInterface.setReleaseId(apiRelease.getId());
            apiInterfaceList.add(apiInterface);
        });
        iApiInterfaceService.saveBatch(apiInterfaceList);
        return true;
    }

    private String rename(String name) {
        String newName;
        int beginIndex = name.lastIndexOf("(");
        int endIndex = name.lastIndexOf(")");
        if (beginIndex > 0 && endIndex > 0) {
            String s = name.substring(beginIndex + 1, endIndex);
            if (Func.isDigit(s)) {
                int index = Integer.parseInt(s);
                index++;
                newName = name.substring(0, beginIndex) + "(" + index + ")";
            } else {
                newName = name + "-副本(1)";
            }
        } else {
            newName = name + "-副本(1)";
        }
        // 检验数据库中是否存在同名作业
        QueryWrapper<ApiRelease> query = new QueryWrapper<>();
        query.eq("name", newName);
        if (this.count(query) > 0) {
            newName = rename(newName);
        }
        return newName;
    }
}
