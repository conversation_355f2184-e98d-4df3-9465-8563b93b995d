package com.mchz.dcas.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * api版本记录，包含产品类型、产品所属公司、产品logo以及产品对接参数（token）等
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("api_release")
public class ApiRelease implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产品类型名称
     */
    @TableField("name")
    private String name;

    /**
     * 对接产品能力
     */
    @TableField("capability")
    private String capability;

    /**
     * 所属公司
     */
    @TableField("company")
    private String company;

    /**
     * 产品logo
     */
    @TableField("logo")
    private String logo;

    /**
     * 产品授权字段参数
     */
    @TableField("auth")
    private String auth;

    /**
     * 产品授权参数配置规则
     */
    @TableField("auth_config")
    private String authConfig;

    /**
     * 能力标签类型
     */
    @TableField("ability")
    private String ability;

    /**
     * 应用标签
     */
    @TableField("app_tag")
    private String appTag;

    /**
     * 是否启用
     */
    @TableField("enable")
    private Boolean enable;

    /**
     * 版本号，从1开始，根据页面触发一次递增1
     */
    @TableField("version")
    private Integer version;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 修改人
     */
    @TableField("update_user")
    private String updateUser;


}
