package com.mchz.dcas.server.model.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * api版本记录，包含产品类型、产品所属公司、产品logo以及产品对接参数（token）等
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-06
 */
@Data
public class ApiReleaseParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "产品对接库id，id不为空表示更新或创建副本，否则视为新增")
    private Long id;

    /**
     * 产品类型名称
     */
    @NotBlank(message = "产品类型名称不能为空")
    @ApiModelProperty(value = "产品类型名称")
    private String name;

    /**
     * 对接产品能力
     */
    @NotBlank(message = "对接产品能力不能为空")
    @ApiModelProperty(value = "对接产品能力")
    private String capability;

    /**
     * 所属公司
     */
    @NotBlank(message = "所属公司不能为空")
    @ApiModelProperty(value = "所属公司")
    private String company;

    /**
     * 产品logo
     */
    @ApiModelProperty(value = "产品logo")
    private String logo;

    /**
     * 产品授权字段参数
     */
    @NotBlank(message = "产品授权字段参数不能为空")
    @ApiModelProperty(value = "产品授权字段参数")
    private String auth;

    /**
     * 授权参数配置列表
     */
    @ApiModelProperty(value = "授权参数配置列表")
    private List<AuthParameter> authConfig;

    /**
     * 能力标签类型
     */
    @ApiModelProperty(value = "能力标签类型")
    private String ability;

    /**
     * 应用标签
     */
    @NotBlank(message = "应用标签不能为空")
    @ApiModelProperty(value = "应用标签")
    private String appTag;

    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private Boolean enable;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;

    @ApiModelProperty(value = "api接口列表")
    @NotEmpty(message = "api接口列表不能为空")
    private List<ApiInterfaceParam> apiInterfaceList;
}
