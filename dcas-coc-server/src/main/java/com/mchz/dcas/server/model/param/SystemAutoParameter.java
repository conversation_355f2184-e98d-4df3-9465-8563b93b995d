package com.mchz.dcas.server.model.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.mchz.dcas.server.enums.AuthConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/7/9 14:12
 * @since 1.5.0
 */
@Data
@JsonTypeName("SYSTEM_AUTO")
@EqualsAndHashCode(callSuper = true)
public class SystemAutoParameter extends AuthParameter {
    /**
     * 系统自动获取类型
     */
    @JsonProperty("auto_type")
    private String autoType;

    public SystemAutoParameter() {
        super();
    }

    public SystemAutoParameter(String key, String name, Boolean inHeader, String autoType) {
        super(key, name, AuthConstants.ParamType.SYSTEM_AUTO.name(), inHeader);
        this.autoType = autoType;
    }
}
