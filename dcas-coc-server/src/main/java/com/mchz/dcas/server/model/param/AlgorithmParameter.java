package com.mchz.dcas.server.model.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.mchz.dcas.server.enums.AuthConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2025/7/9 14:47
 * @since 1.0.0
 */
@Data
@JsonTypeName("ALGORITHM")
@EqualsAndHashCode(callSuper = true)
public class AlgorithmParameter extends AuthParameter{
    /**
     * 算法类型
     */
    @JsonProperty("algorithm_type")
    private String algorithmType;

    /**
     * 计算公式 eg:{access-time}-{access-key-secret}
     */
    private String formula;

    public AlgorithmParameter() {
        super();
    }

    public AlgorithmParameter(String key, Boolean inHeader,
                              String algorithmType, String formula) {
        super(key, AuthConstants.ParamType.ALGORITHM.name(), inHeader);
        this.algorithmType = algorithmType;
        this.formula = formula;
    }
}
