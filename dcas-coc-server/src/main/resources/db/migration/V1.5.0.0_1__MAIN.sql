UPDATE public.sys_config SET value = '2.1.3.0' WHERE key = 'terminal.library.tag';

INSERT INTO public.tag_type (id, name, introduce, type, enable, update_time, status)
VALUES (7, '对接产品能力', '对接产品能力', 'I', true, DEFAULT, 0) on conflict (id) do nothing;
INSERT INTO public.tag_type (id, name, introduce, type, enable, update_time, status)
VALUES (8, '对接公司名称', '对接公司名称', 'I', true, DEFAULT, 0) on conflict (id) do nothing;
INSERT INTO public.tag_type (id, name, introduce, type, enable, update_time, status)
VALUES (9, '对接产品参数', '对接产品参数', 'I', true, DEFAULT, 0) on conflict (id) do nothing;
INSERT INTO public.tag_type (id, name, introduce, type, enable, update_time, status)
VALUES (10, '风险类型', '风险类型', 'I', true, DEFAULT, 0) on conflict (id) do nothing;


CREATE TABLE if not exists public.api_release (
	id bigserial NOT NULL,
	name varchar(50) NOT NULL,
	capability varchar(255) NOT NULL,
	company varchar(255) NOT NULL,
	logo text NULL,
	auth varchar(50) NOT NULL,
	ability varchar(255) NULL,
	app_tag varchar(50) NOT NULL,
	version int NOT NULL,
	enable boolean NOT NULL,
	create_time timestamp(6) NOT NULL,
	update_time timestamp(6) NOT NULL,
	create_user varchar(50) NOT NULL,
	update_user varchar(50) NOT NULL,
	CONSTRAINT api_release_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.api_release IS 'api版本记录，包含产品类型、产品所属公司、产品logo以及产品对接参数（token）等';

-- Column comments

COMMENT ON COLUMN public.api_release.id IS '主键ID';
COMMENT ON COLUMN public.api_release.name IS '产品类型名称';
COMMENT ON COLUMN public.api_release.capability IS '对接产品能力';
COMMENT ON COLUMN public.api_release.company IS '所属公司';
COMMENT ON COLUMN public.api_release.logo IS '产品logo';
COMMENT ON COLUMN public.api_release.auth IS '产品授权字段参数';
COMMENT ON COLUMN public.api_release.ability IS '能力标签类型';
COMMENT ON COLUMN public.api_release.app_tag IS '应用标签';
COMMENT ON COLUMN public.api_release.version IS '版本号，从1开始，根据页面触发一次递增1';
COMMENT ON COLUMN public.api_release.enable IS '启用';
COMMENT ON COLUMN public.api_release.create_time IS '创建时间';
COMMENT ON COLUMN public.api_release.update_time IS '更新时间';
COMMENT ON COLUMN public.api_release.create_user IS '创建人';
COMMENT ON COLUMN public.api_release.update_user IS '修改人';


CREATE TABLE if not exists public.api_interface (
	id bigserial NOT NULL,
	release_id bigint NOT NULL,
	api_name varchar(255) NOT NULL,
	api_method varchar(255) NOT NULL,
	api_path varchar(255) NOT NULL,
	req_params varchar(4000) NULL,
	req_result text NULL,
	sort int NOT NULL,
	create_time timestamp(6) NOT NULL,
	update_time timestamp(6) NOT NULL,
	create_user varchar(50) NULL,
	update_user varchar(50) NULL,
	CONSTRAINT api_interface_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.api_interface IS '对接产品api接口';

-- Column comments

COMMENT ON COLUMN public.api_interface.id IS '主键ID';
COMMENT ON COLUMN public.api_interface.release_id IS '关联api版本记录ID';
COMMENT ON COLUMN public.api_interface.api_name IS '接口名';
COMMENT ON COLUMN public.api_interface.api_method IS '接口请求方式';
COMMENT ON COLUMN public.api_interface.api_path IS '接口路径';
COMMENT ON COLUMN public.api_interface.req_params IS '请求参数';
COMMENT ON COLUMN public.api_interface.req_result IS '请求结果';
COMMENT ON COLUMN public.api_interface.sort IS '接口排序';
COMMENT ON COLUMN public.api_interface.create_time IS '创建时间';
COMMENT ON COLUMN public.api_interface.update_time IS '修改时间';
COMMENT ON COLUMN public.api_interface.create_user IS '创建用户';
COMMENT ON COLUMN public.api_interface.update_user IS '修改用户';


Do $$
BEGIN
BEGIN
ALTER TABLE public.risk_strategy ADD risk_type varchar(255) NULL;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column risk_type already exists in risk_strategy.';
END;
END;
$$;
COMMENT ON COLUMN public.risk_strategy.risk_type IS '风险类型';

ALTER TABLE public.rule_config ALTER COLUMN formula TYPE text USING formula::text;


Do $$
BEGIN
BEGIN
ALTER TABLE public.api_interface ADD api_type int DEFAULT 1 NOT NULL;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column api_type already exists in api_interface.';
END;
END;
$$;
COMMENT ON COLUMN public.api_interface.api_type IS '接口类型颗粒度：1-验证能力；2-具体结果';

--修改检查作业评估内容脱敏加密技术检测父级id
UPDATE public.template_service_content SET parent_id=3 WHERE id=7;
UPDATE public.template_service_content SET parent_id=3 WHERE id=8;
UPDATE public.template_service_content SET parent_id=3 WHERE id=15;
UPDATE public.template_service_content SET parent_id=3 WHERE id=16;

Do $$
BEGIN
BEGIN
alter table public.api_release add auth_config text;
EXCEPTION
            WHEN duplicate_column THEN RAISE NOTICE 'column auth_config already exists in api_release.';
END;
END;
$$;
comment on column public.api_release.auth_config is '鉴权参数配置规则';



