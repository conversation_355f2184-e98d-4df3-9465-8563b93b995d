<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mchz.dcas.server.mapper.ApiReleaseMapper">

    <select id="selectAllEnableCapabilityKeyValue" resultType="com.mchz.dcas.server.model.vo.KeyValue">
        select  (t.type_id || ar.capability)::INTEGER as key, t."name" as value from api_release ar left join tag t on t.id = ar.capability::INTEGER where ar."enable" is true
    </select>
</mapper>
